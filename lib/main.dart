import 'dart:ui';
import 'package:SAiWELL/common_controllers/connectivity.dart';
import 'package:SAiWELL/firebase_options.dart';
import 'package:SAiWELL/modules/home/<USER>/home_controller.dart';
import 'package:SAiWELL/modules/ppg/controller/ppg_controller.dart';
import 'package:SAiWELL/modules/splash/splash_screen.dart';
import 'package:SAiWELL/services/deep_link_service.dart';
import 'package:SAiWELL/services/notifications/messaging/fcm_service.dart';
import 'package:SAiWELL/services/firebase_remote_config_service.dart';
import 'package:SAiWELL/services/notifications/core/notification_manager.dart';
import 'package:SAiWELL/services/native_communicator.dart';
import 'package:SAiWELL/utils/const/app_const.dart';
import 'package:SAiWELL/utils/const/app_theme.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:health/health.dart';
import 'package:native_device_orientation/native_device_orientation.dart';
import 'app_routes.dart';
import 'common_controllers/global_controller.dart';
import 'constants/constant.dart';
import 'modules/update_available/package_info.dart';
import 'package:flutter/rendering.dart';
import 'modules/splash/binding/splash_binding.dart';
import 'services/user_session_service.dart';
import 'dart:io';
import 'package:SAiWELL/services/background_fetch_service.dart';

Future<void> initializeFirebaseCrashlytics() async {
  if (isProdEnv) {
    try {
      FlutterError.onError = (errorDetails) {
        try {
          FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
        } catch (e) {
          print("Failed to record error to Crashlytics: $e");
        }
      };

      PlatformDispatcher.instance.onError = (error, stack) {
        try {
          if (error is FormatException) {
            FirebaseCrashlytics.instance.recordError(
              "FormatException: ${error.message}",
              stack,
              fatal: false,
              reason: "Handled FormatException",
            );
          } else {
            FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
          }
        } catch (e) {
          print("Failed to record error to Crashlytics: $e");
        }
        return true;
      };

      // Set Crashlytics collection enabled
      await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
    } catch (e) {
      print("Failed to initialize Firebase Crashlytics: $e");
    }
  }
}

Future<void> initializeFirebase() async {
  try {
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
          name: "SAiWell_Flutter");
    }
  } catch (e) {
    print("Failed to initialize Firebase: $e");
    // Continue app initialization even if Firebase fails
  }
}

Future<void> initializeServices() async {
  try {
    final packageInfo = PackageInfoSetup();
    await packageInfo.initialize();
  } catch (e) {
    print("Failed to initialize package info: $e");
  }

  try {
    final firebaseRemoteConfigService = FirebaseRemoteConfigService();
    await firebaseRemoteConfigService.initialize();
  } catch (e) {
    print("Failed to initialize remote config: $e - continuing with defaults");
  }

  try {
    Get.put(UserSessionService(), permanent: true);
    Get.put(GlobalController(), permanent: true);
    NativeCommunicator nativeCommunicator = NativeCommunicator();
    nativeCommunicator.initializeBgServiceForApple();

    Get.put(
        ConnectivityController(displayType: NoInternetDisplayType.fullScreen),
        permanent: true);
    Get.lazyPut(() => PPGController(), fenix: true);

    // Initialize deep link service as early as possible
    try {
      final deepLinkService = DeepLinkService();
      // Store in GetX for global access
      Get.put(deepLinkService, permanent: true);
      await deepLinkService.initialize();
      print("Deep link service initialized successfully");
    } catch (e) {
      print("Failed to initialize deep link service: $e");
    }

    Get.put(HomeController(), permanent: true);
  } catch (e) {
    print("Failed to initialize controllers: $e");
  }

  try {
    await FcmService().initialise();
  } catch (e) {
    print("Failed to initialize FCM service: $e");
  }

  try {
    await NotificationManager.initialize();
    // Conditionally schedule hourly health notifications only for users who have connected to a ring
    await NotificationManager.startDailyNotifications();
    print(
        "Notification manager initialized and hourly notifications scheduled conditionally");
  } catch (e) {
    print("Failed to initialize notification scheduler: $e");
  }
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  bool isAuthenticated = await Health().isHealthDataInBackgroundAuthorized();
  if (!isAuthenticated) {
    await Health().requestHealthDataInBackgroundAuthorization();
  }

  if (Platform.isAndroid) {
    BackgroundFetchService.initialize();
  }

  await initializeFirebase();
  await initializeFirebaseCrashlytics();
  await initializeServices();

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]).then((_) {
    runApp(
      NativeDeviceOrientationReader(
        useSensor: false,
        builder: (BuildContext context) {
          return const MyApp();
        },
      ),
    );
  });
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Create a key to force rebuild of app when needed
    return GetMaterialApp(
      title: AppConst.appName,
      getPages: AppRoutes.getPages,
      debugShowCheckedModeBanner: false,
      initialRoute: SplashScreen.routeName,
      unknownRoute: GetPage(
        name: SplashScreen.routeName,
        page: () => const SplashScreen(),
        bindings: SplashBinding.binding,
      ),
      theme: buildAppTheme(),
    );
  }
}
